/// <reference types="vite/client" />

declare module '*.vue' {
  import type { DefineComponent } from 'vue'
  const component: DefineComponent<{}, {}, any>
  export default component
}

// API response types
interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
  success?: boolean
}

// Common types
interface PaginationParams {
  pageIndex: number
  pageSize: number
  count?: number
}

// Component instance types
declare global {
  interface ComponentCustomProperties {
    parseTime: (time: string | number) => string
  }
}
