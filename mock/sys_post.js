import Mock from 'mockjs'

const mock = {
  'list|10': [
    {
      'roleId|+1': 1,
      'roleName|1': ['系统管理员', '福州系统运维', '产品管理', '前端开发', '后端开发'],
      roleKey: 'admin',
      'roleSort|+1': 0,
      'status|0-1': 0,
      createAt: `${Mock.Random.date()} ${Mock.Random.time()}`,
    },
  ],
}

// 菜单数据
const menuData = [
  {
    menuId: 1,
    menuName: 'permission-management',
    title: '权限管理',
    path: '/admin',
    component: '/admin/sys-api/index',
    menuType: 'C',
    visible: '0',
    icon: 'SettingOutlined',
  },
  {
    menuId: 2,
    menuName: 'xunlei-cloud',
    title: '迅雷云盘',
    path: '/xunlei',
    menuType: 'M',
    visible: '0',
    icon: 'CloudOutlined',
    children: [
      {
        menuId: 21,
        menuName: 'complaint-management',
        title: '客诉管理',
        path: '/xunlei/complaint',
        menuType: 'M',
        visible: '0',
        icon: 'CustomerServiceOutlined',
        children: [
          {
            menuId: 211,
            menuName: 'cloud-related-query',
            title: '云盘相关查询',
            path: '/xunlei/complaint/cloud-query',
            component: '/xunlei/complaint/cloud-query',
            menuType: 'C',
            visible: '0',
            icon: 'SearchOutlined',
          },
          {
            menuId: 212,
            menuName: 'user-status-query',
            title: '用户状态查询',
            path: '/xunlei/complaint/user-status',
            component: '/xunlei/complaint/user-status',
            menuType: 'C',
            visible: '0',
            icon: 'UserOutlined',
          },
          {
            menuId: 213,
            menuName: 'cloud-file-query',
            title: '云盘文件查询',
            path: '/xunlei/complaint/file-query',
            component: '/xunlei/complaint/file-query',
            menuType: 'C',
            visible: '0',
            icon: 'FileSearchOutlined',
          },
        ],
      },
    ],
  },
  {
    menuId: 3,
    menuName: 'nas-cloud',
    title: 'NAS云盘',
    path: '/nas',
    component: '/nas/index',
    menuType: 'C',
    visible: '0',
    icon: 'DatabaseOutlined',
  },
]

const login = {
  url: '/api/admin/post',
  method: 'get',
  timeout: 500,
  statusCode: 200,
  response: {
    code: 200,
    message: '登陆成功',
    data: Mock.toJSONSchema(mock).template,
  },
}

// 获取菜单接口
const getUserMenuRole = {
  url: '/api/v1/menurole',
  method: 'get',
  timeout: 500,
  statusCode: 200,
  response: {
    code: 200,
    message: '获取菜单成功',
    data: menuData,
  },
}

export default [login, getUserMenuRole]
