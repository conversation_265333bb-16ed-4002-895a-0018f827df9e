<template>
  <a-modal
    v-model:open="deleteVisible"
    :title="data.length > 1 ? '批量删除' : '删除'"
    @cancel="handleClose"
    width="400px"
  >
    <a-form
      :model="formData"
      :rules="rules"
      ref="modalFormRef"
      size="middle"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-alert type="warning" style="margin-bottom: 16px" v-if="data.length > 1"
        >您正在批量删除，请确认删除数据是否正确</a-alert
      >
      <a-form-item label="摘要">
        <a-tag color="default" style="padding: 0 0px; margin-right: 5px">
          <template #icon>
            <a-tag color="blue">数量</a-tag>
          </template>
          <a-tag style="margin-left: -4px">{{ data.length }}</a-tag>
        </a-tag>
      </a-form-item>
      <a-form-item name="deleteConfirmation" label="删除确认">
        <a-input
          v-model:value="formData.deleteConfirmation"
          placeholder="请输入 DELETE 以确认删除"
        ></a-input>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleClose">
        <template #icon><CloseOutlined /></template>取消
      </a-button>
      <a-button type="primary" @click="handleConfirm">
        <template #icon><CheckOutlined /></template>确认
      </a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { CloseOutlined, CheckOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue'

interface DeleteData {
  deleteConfirmation?: string
  [key: string]: unknown
}

interface Props {
  data: DeleteData[]
  visible?: boolean
  apiDelete?: (params: { ids: DeleteData[] }) => Promise<unknown>
}

interface Emits {
  deleteVisibleChange: [value: boolean]
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
})

const emits = defineEmits<Emits>()

const { data } = toRefs(props)

// 表单引用
const modalFormRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  deleteConfirmation: '',
})

// Akiraka 20230210 对话框弹出
const deleteVisible = ref<boolean>(false)

// Akiraka 20230210 删除数据校验
const rules = reactive({
  deleteConfirmation: [
    {
      required: true,
      validator: (value: string, cb: (error?: string) => void) => {
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            if ('DELETE' !== value) {
              cb('请输入 DELETE')
            }
            resolve()
          }, 500)
        })
      },
    },
  ],
})

// Akiraka 20230210 关闭弹窗
const handleClose = (): void => {
  emits('deleteVisibleChange', (deleteVisible.value = false))
  // Akiraka 20230210 关闭弹窗
  deleteVisible.value = false
  // 重置表单
  formData.deleteConfirmation = ''
}

// Akiraka 20230210 监听事件
watch(
  () => props.visible,
  (value) => {
    if (value) {
      // Akiraka 20230210 打开或关闭对话框
      deleteVisible.value = value
    }
  },
)

// Akiraka 20230210 确认按钮 => 开始数据检查
const handleConfirm = (): void => {
  modalFormRef.value
    ?.validate()
    .then(() => {
      // Akiraka 20230210 请求接口
      props.apiDelete?.({ ids: data.value }).then(() => {
        // Akiraka 20230210 关闭弹窗
        deleteVisible.value = false
        message.success('数据删除成功')
      })
    })
    .catch(() => {
      // 验证失败
    })
}
</script>

<style lang="less" scoped></style>
