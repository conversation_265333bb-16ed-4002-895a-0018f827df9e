<template>
  <Icon :icon="iconName" :color="color" :style="style" />
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'

defineOptions({ name: 'Iconify' })

interface Props {
  icon: string
  color?: string
  width?: string
  height?: string
}

const props = withDefaults(defineProps<Props>(), {
  color: '',
  width: '',
  height: '',
})

const style = computed(() => ({
  width: props.width,
  height: props.height,
}))

const iconName = computed(() => props.icon)
</script>
