import request from '@/utils/request'

interface TableQuery {
  pageIndex?: number
  pageSize?: number
  tableName?: string
  [key: string]: unknown
}

interface GenTableData {
  tableId?: string | number
  tableName?: string
  tableComment?: string
  className?: string
  functionAuthor?: string
  functionName?: string
  options?: string
  [key: string]: unknown
}

// 查询生成表数据
export function listTable(query: TableQuery) {
  return request({
    url: '/api/v1/sys/tables/page',
    method: 'get',
    params: query,
  })
}

// 查询db数据库列表
export function listDbTable(query: TableQuery) {
  return request({
    url: '/api/v1/db/tables/page',
    method: 'get',
    params: query,
  })
}

// 查询表详细信息
export function getGenTable(tableId: string | number) {
  return request({
    url: '/api/v1/sys/tables/info/' + tableId,
    method: 'get',
  })
}

export function getGenTableInfo(tablename: string) {
  return request({
    url: '/api/v1/sys/tables?tableName=' + tablename,
    method: 'get',
  })
}

// 修改代码生成信息
export function updateGenTable(data: GenTableData) {
  return request({
    url: '/api/v1/sys/tables/info',
    method: 'put',
    data: data,
  })
}

// 导入表
export function importTable(data: TableQuery) {
  return request({
    url: '/api/v1/sys/tables/info',
    method: 'post',
    params: data,
  })
}

// 预览生成代码
export function previewTable(tableId: string | number) {
  return request({
    url: '/api/v1/gen/preview/' + tableId,
    method: 'get',
  })
}

// 删除表数据
export function delTable(tableId: string | number) {
  return request({
    url: '/api/v1/sys/tables/info/' + tableId,
    method: 'delete',
  })
}

// 生成代码到项目
export function toProjectTable(tableId: string | number) {
  return request({
    url: '/api/v1/gen/toproject/' + tableId,
    method: 'get',
  })
}

// 生成接口数据到迁移脚本
export function apiToFile(tableId: string | number) {
  return request({
    url: '/api/v1/gen/apitofile/' + tableId,
    method: 'get',
  })
}

export function toProjectTableCheckRole(tableId: string | number, ischeckrole: boolean) {
  return request({
    url: '/api/v1/gen/toproject/' + tableId + '?ischeckrole=' + ischeckrole,
    method: 'get',
  })
}

// 生成菜单到数据库
export function toDBTable(tableId: string | number) {
  return request({
    url: '/api/v1/gen/todb/' + tableId,
    method: 'get',
  })
}

export function getTableTree() {
  return request({
    url: '/api/v1/gen/tabletree',
    method: 'get',
  })
}
