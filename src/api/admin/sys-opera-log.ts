import request from '../../utils/request'

const url = '/api/v1/sys-opera-log'

interface OperaLogParams {
  [key: string]: unknown
}

interface OperaLogData {
  [key: string]: unknown
}

export function getSysOperaLog(params: OperaLogParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function removeSysOperaLog(data: OperaLogData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}
