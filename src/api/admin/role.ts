import request from '../../utils/request'

const url = '/api/v1/role'

interface RoleParams {
  [key: string]: unknown
}

interface RoleData {
  [key: string]: unknown
}

export function getRole(params: RoleParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addRole(data: RoleData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeRole(data: RoleData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updateRole(data: RoleData, id: string | number) {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}

export function updateRoleScoped(data: RoleData) {
  return request({
    url: '/api/v1/roledatascope',
    method: 'put',
    data,
  })
}

export function getRoleMenuTree(params: RoleParams, roleId: string | number) {
  return request({
    url: `/api/v1/roleMenuTreeselect/${roleId}`,
    method: 'get',
    params,
  })
}
