import request from '../../utils/request'

const url = '/api/v1/sys-api'

interface ApiParams {
  [key: string]: unknown
}

interface ApiData {
  [key: string]: unknown
}

export function getSysApi(params: ApiParams): Promise<ApiResponse> {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addSysApi(data: ApiData): Promise<ApiResponse> {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeSysApi(data: ApiData): Promise<ApiResponse> {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updateSysApi(data: ApiData, id: string | number): Promise<ApiResponse> {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}
