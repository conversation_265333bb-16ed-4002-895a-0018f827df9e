import request from '../../utils/request'

const url = '/api/v1/menu'

interface MenuQuery {
  pageIndex?: number
  pageSize?: number
  menuName?: string
  status?: number
  [key: string]: unknown
}

interface MenuData {
  menuId?: string | number
  menuName?: string
  parentId?: string | number
  orderNum?: number
  path?: string
  component?: string
  query?: string
  isFrame?: number
  isCache?: number
  menuType?: string
  visible?: number
  status?: number
  perms?: string
  icon?: string
  [key: string]: unknown
}

export function getMenu(params: MenuQuery) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function getMenuDetails(menuId: string | number) {
  return request({
    url: `${url}/${menuId}`,
    method: 'get',
  })
}

export function addMenu(data: MenuData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeMenu(data: { menuIds: (string | number)[] }) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

/**
 * 修改菜单
 * @param {MenuData} data
 * @param {string | number} id
 * @returns
 */
export function updateMenu(data: MenuData, id: string | number) {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}
