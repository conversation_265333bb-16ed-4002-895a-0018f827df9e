import request from '../../utils/request'

const url = '/api/v1/dept'

interface DeptParams {
  [key: string]: unknown
}

interface DeptData {
  [key: string]: unknown
}

export function getDept(params: DeptParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addDept(data: DeptData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeDept(data: DeptData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updateDept(data: DeptData, id: string | number) {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}
