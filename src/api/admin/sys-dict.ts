import request from '../../utils/request'

const url = '/api/v1/dict/type'

interface DictTypeParams {
  [key: string]: unknown
}

interface DictTypeData {
  [key: string]: unknown
}

export function getDictType(params: DictTypeParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addDictType(data: DictTypeData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeDictType(data: DictTypeData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updateDictType(data: DictTypeData, id: string | number) {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}

// 获取字典选择框列表
export function optionselect() {
  return request({
    url: '/api/v1/dict/type-option-select',
    method: 'get',
  })
}
