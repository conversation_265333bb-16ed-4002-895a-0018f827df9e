import request from '../../utils/request'

const url = '/api/v1/config'

interface ConfigParams {
  [key: string]: unknown
}

interface ConfigData {
  [key: string]: unknown
}

export function getSysConfig(params: ConfigParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addSysConfig(data: ConfigData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeSysConfig(data: ConfigData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updateSysConfig(data: ConfigData, id: string | number) {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}
