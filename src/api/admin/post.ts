import request from '../../utils/request'

const url = '/api/v1/post'

interface PostParams {
  [key: string]: unknown
}

interface PostData {
  [key: string]: unknown
}

export function getPost(params: PostParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addPost(data: PostData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removePost(data: PostData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updatePost(data: PostData, id: string | number) {
  return request({
    url: `${url}/${id}`,
    method: 'put',
    data,
  })
}
