import request from '../../utils/request'

const url = '/api/v1/sys-user'

interface UserParams {
  [key: string]: unknown
}

interface UserData {
  [key: string]: unknown
}

export function getUser(params: UserParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function getInfo() {
  return request({
    url: '/api/v1/getinfo',
    method: 'get',
  })
}

export function addUser(data: UserData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function removeUser(data: UserData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}

export function updateUser(data: UserData) {
  return request({
    url,
    method: 'put',
    data,
  })
}

export function updateUserStatus(data: UserData) {
  return request({
    url: '/api/v1/user/status',
    method: 'put',
    data,
  })
}

export function resetUserPwd(data: UserData) {
  return request({
    url: '/api/v1/user/pwd/reset',
    method: 'put',
    data,
  })
}

// 获取当前登录用户信息
export function getCurrentUser(uid: string | number) {
  return request({
    url: `${url}/${uid}`,
    method: 'get',
  })
}
