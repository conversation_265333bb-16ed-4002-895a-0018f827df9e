import request from '../../utils/request'

const url = '/api/v1/sys-login-log'

interface LoginLogParams {
  [key: string]: unknown
}

interface LoginLogData {
  [key: string]: unknown
}

export function getSysLoginLog(params: LoginLogParams) {
  return request({
    url,
    method: 'GET',
    params,
  })
}

export function removeSysLoginLog(data: LoginLogData) {
  return request({
    url,
    method: 'DELETE',
    data,
  })
}
