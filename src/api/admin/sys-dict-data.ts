import request from '@/utils/request'

const url = '/api/v1/dict/data'

interface DictDataParams {
  [key: string]: unknown
}

interface DictDataData {
  [key: string]: unknown
}

export function getDictData(params: DictDataParams) {
  return request({
    url,
    method: 'get',
    params,
  })
}

export function addDictData(data: DictDataData) {
  return request({
    url,
    method: 'post',
    data,
  })
}

export function updateDictData(data: DictDataData, dictCode: string | number) {
  return request({
    url: `${url}/${dictCode}`,
    method: 'put',
    data,
  })
}

export function deleteDictData(data: DictDataData) {
  return request({
    url,
    method: 'delete',
    data,
  })
}
