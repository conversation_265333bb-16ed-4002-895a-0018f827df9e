import request from '@/utils/request'

interface SysJobQuery {
  [key: string]: unknown
}

interface SysJobData {
  [key: string]: unknown
}

// 查询SysJob列表
export function listSysJob(query: SysJobQuery) {
  return request({
    url: '/api/v1/sysjob',
    method: 'get',
    params: query,
  })
}

// 查询SysJob详细
export function getSysJob(jobId: string | number) {
  return request({
    url: '/api/v1/sysjob/' + jobId,
    method: 'get',
  })
}

// 新增SysJob
export function addSysJob(data: SysJobData) {
  return request({
    url: '/api/v1/sysjob',
    method: 'post',
    data: data,
  })
}

// 修改SysJob
export function updateSysJob(data: SysJobData) {
  return request({
    url: '/api/v1/sysjob',
    method: 'put',
    data: data,
  })
}

// 删除SysJob
export function delSysJob(data: SysJobData) {
  return request({
    url: '/api/v1/sysjob',
    method: 'delete',
    data: data,
  })
}

// 移除SysJob
export function removeJob(jobId: string | number) {
  return request({
    url: '/api/v1/job/remove/' + jobId,
    method: 'get',
  })
}

// 启动SysJob
export function startJob(jobId: string | number) {
  return request({
    url: '/api/v1/job/start/' + jobId,
    method: 'get',
  })
}
