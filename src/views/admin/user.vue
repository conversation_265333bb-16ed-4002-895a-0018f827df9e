<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <p>管理系统用户账户和权限</p>
    </div>
    
    <div class="content-card">
      <div class="search-bar">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input 
              v-model:value="searchForm.username" 
              placeholder="请输入用户名"
              allow-clear
            />
          </a-col>
          <a-col :span="6">
            <a-select 
              v-model:value="searchForm.status" 
              placeholder="请选择状态"
              allow-clear
            >
              <a-select-option value="1">启用</a-select-option>
              <a-select-option value="0">禁用</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="12">
            <a-space>
              <a-button type="primary" @click="handleSearch">
                <SearchOutlined />
                搜索
              </a-button>
              <a-button @click="handleReset">
                <ReloadOutlined />
                重置
              </a-button>
              <a-button type="primary" @click="handleAdd">
                <PlusOutlined />
                新增用户
              </a-button>
            </a-space>
          </a-col>
        </a-row>
      </div>
      
      <a-table 
        :columns="columns" 
        :data-source="userList" 
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="record.status === '1' ? 'green' : 'red'">
              {{ record.status === '1' ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button type="link" size="small" @click="handleEdit(record)">
                <EditOutlined />
                编辑
              </a-button>
              <a-button type="link" size="small" danger @click="handleDelete(record)">
                <DeleteOutlined />
                删除
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined,
  EditOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue'

// 搜索表单
const searchForm = ref({
  username: '',
  status: undefined
})

// 表格列定义
const columns = [
  {
    title: '用户ID',
    dataIndex: 'userId',
    key: 'userId',
    width: 80,
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
  },
  {
    title: '昵称',
    dataIndex: 'nickName',
    key: 'nickName',
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 80,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
  },
]

// 用户列表数据
const userList = ref([
  {
    userId: 1,
    username: 'admin',
    nickName: '系统管理员',
    email: '<EMAIL>',
    phone: '13800138000',
    status: '1',
    createTime: '2024-01-01 10:00:00',
  },
  {
    userId: 2,
    username: 'user001',
    nickName: '普通用户',
    email: '<EMAIL>',
    phone: '13800138001',
    status: '1',
    createTime: '2024-01-02 10:00:00',
  },
  {
    userId: 3,
    username: 'user002',
    nickName: '测试用户',
    email: '<EMAIL>',
    phone: '13800138002',
    status: '0',
    createTime: '2024-01-03 10:00:00',
  },
])

const loading = ref(false)
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 3,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`,
})

// 搜索
const handleSearch = () => {
  console.log('搜索:', searchForm.value)
  // 这里可以调用 API 进行搜索
}

// 重置
const handleReset = () => {
  searchForm.value = {
    username: '',
    status: undefined
  }
}

// 新增用户
const handleAdd = () => {
  console.log('新增用户')
  // 这里可以打开新增用户的弹窗
}

// 编辑用户
const handleEdit = (record: any) => {
  console.log('编辑用户:', record)
  // 这里可以打开编辑用户的弹窗
}

// 删除用户
const handleDelete = (record: any) => {
  console.log('删除用户:', record)
  // 这里可以显示确认删除的弹窗
}

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.value = { ...pagination.value, ...pag }
  // 这里可以重新加载数据
}
</script>

<style lang="scss" scoped>
.user-management {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
  
  h2 {
    margin: 0 0 8px 0;
    color: #2d5aa0;
    font-size: 24px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.content-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-bar {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
}
</style>
