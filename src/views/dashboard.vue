<template>
  <div class="dashboard-container">
    <div class="welcome-card">
      <h1 class="welcome-title">欢迎使用迅雷客服系统</h1>
      <p class="welcome-desc">这是一个基于 Vue 3 + Ant Design Vue 的现代化客服管理系统</p>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <UserOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-number">1,234</div>
            <div class="stat-label">在线用户</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <MessageOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-number">5,678</div>
            <div class="stat-label">今日消息</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <CheckCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-number">98.5%</div>
            <div class="stat-label">解决率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <ClockCircleOutlined />
          </div>
          <div class="stat-content">
            <div class="stat-number">2.3分钟</div>
            <div class="stat-label">平均响应时间</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="content-grid">
      <div class="chart-card">
        <h3>系统概览</h3>
        <p>这里可以放置图表和数据可视化内容</p>
      </div>
      
      <div class="activity-card">
        <h3>最近活动</h3>
        <div class="activity-list">
          <div class="activity-item">
            <div class="activity-time">10:30</div>
            <div class="activity-content">用户张三提交了新的工单</div>
          </div>
          <div class="activity-item">
            <div class="activity-time">10:25</div>
            <div class="activity-content">客服李四处理了工单 #12345</div>
          </div>
          <div class="activity-item">
            <div class="activity-time">10:20</div>
            <div class="activity-content">系统自动分配了新的工单</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  UserOutlined, 
  MessageOutlined, 
  CheckCircleOutlined, 
  ClockCircleOutlined 
} from '@ant-design/icons-vue'
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 0;
}

.welcome-card {
  background: white;
  border-radius: 8px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-title {
  font-size: 28px;
  font-weight: bold;
  color: #2d5aa0;
  margin-bottom: 8px;
}

.welcome-desc {
  color: #666;
  font-size: 16px;
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  background: linear-gradient(135deg, #2d5aa0 0%, #1c3f7a 100%);
  border-radius: 8px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 32px;
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.chart-card,
.activity-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-card h3,
.activity-card h3 {
  margin-bottom: 16px;
  color: #2d5aa0;
  font-size: 18px;
  font-weight: 600;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-time {
  color: #999;
  font-size: 12px;
  min-width: 40px;
}

.activity-content {
  color: #666;
  font-size: 14px;
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
}
</style>
