<template>
  <div class="app-container">
    <a-card :bordered="false" class="cardStyle">
      <div class="akaInfoTitle">NAS云盘</div>
      <div class="akaInfoDesc">NAS云盘管理和监控</div>

      <a-empty style="margin-top: 60px" description="功能开发中..." />
    </a-card>
  </div>
</template>

<script setup lang="ts">
// NAS云盘页面
</script>

<style scoped lang="scss">
.app-container {
  .cardStyle {
    margin-bottom: 16px;
  }
}
</style>
