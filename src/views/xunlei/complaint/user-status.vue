<template>
  <div class="app-container">
    <a-card :bordered="false" class="cardStyle">
      <div class="akaInfoTitle">用户状态查询</div>
      <div class="akaInfoDesc">请输入 用户UID/设备ID，然后点击查询按钮</div>

      <a-form
        :model="queryForm"
        layout="horizontal"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        style="margin-top: 24px; max-width: 600px"
      >
        <a-form-item label="用户UID:">
          <a-input v-model:value="queryForm.userUid" placeholder="请输入" style="width: 200px" />
        </a-form-item>

        <a-form-item label="设备ID:">
          <a-input v-model:value="queryForm.deviceId" placeholder="请输入" style="width: 200px" />
        </a-form-item>

        <a-form-item :wrapper-col="{ span: 18, offset: 6 }">
          <a-button type="primary" @click="handleQuery" :loading="loading"> 查询 </a-button>
          <a-button style="margin-left: 8px" @click="handleReset"> 重置 </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 查询结果区域 -->
    <a-card v-if="searchResult" :bordered="false" class="cardStyle" style="margin-top: 16px">
      <template #title>查询结果</template>

      <a-table :columns="columns" :data-source="searchResult" :pagination="false" row-key="id" />
    </a-card>

    <!-- 空状态 -->
    <a-empty
      v-if="!searchResult && searched"
      style="margin-top: 60px"
      description="请输入 用户UID/设备ID，然后点击查询按钮"
    />
  </div>
</template>

<script setup lang="ts">
interface QueryForm {
  userUid: string
  deviceId: string
}

interface UserStatusResult {
  id: string
  userUid: string
  deviceId: string
  status: string
  lastLogin: string
  accountType: string
  region: string
}

const queryForm = ref<QueryForm>({
  userUid: '',
  deviceId: '',
})

const loading = ref(false)
const searched = ref(false)
const searchResult = ref<UserStatusResult[]>()

// 表格列定义
const columns = [
  {
    title: '用户UID',
    dataIndex: 'userUid',
    key: 'userUid',
  },
  {
    title: '设备ID',
    dataIndex: 'deviceId',
    key: 'deviceId',
  },
  {
    title: '用户状态',
    dataIndex: 'status',
    key: 'status',
    customRender: ({ text }: { text: string }) => {
      const statusMap: Record<string, { color: string; text: string }> = {
        active: { color: 'green', text: '正常' },
        inactive: { color: 'red', text: '停用' },
        suspended: { color: 'orange', text: '暂停' },
      }
      const status = statusMap[text] || { color: 'default', text: '未知' }
      return h('a-tag', { color: status.color }, status.text)
    },
  },
  {
    title: '最后登录时间',
    dataIndex: 'lastLogin',
    key: 'lastLogin',
  },
  {
    title: '账户类型',
    dataIndex: 'accountType',
    key: 'accountType',
  },
  {
    title: '地区',
    dataIndex: 'region',
    key: 'region',
  },
]

// 查询处理
const handleQuery = async () => {
  if (!queryForm.value.userUid && !queryForm.value.deviceId) {
    window.$message?.warning('请至少输入用户UID或设备ID')
    return
  }

  loading.value = true
  searched.value = true

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 模拟返回数据
    const mockData: UserStatusResult[] = [
      {
        id: '1',
        userUid: queryForm.value.userUid || 'UID123456789',
        deviceId: queryForm.value.deviceId || 'DEV987654321',
        status: 'active',
        lastLogin: '2024-01-15 14:30:25',
        accountType: '高级会员',
        region: '广东省深圳市',
      },
    ]

    searchResult.value = mockData
  } catch (error) {
    window.$message?.error('查询失败，请重试')
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  queryForm.value = {
    userUid: '',
    deviceId: '',
  }
  searchResult.value = undefined
  searched.value = false
}
</script>

<style scoped lang="scss">
.app-container {
  .cardStyle {
    margin-bottom: 16px;
  }
}
</style>
