// LocalStorage

export const storage = {
  getKeys(): (string | null)[] {
    const keys: (string | null)[] = []
    for (let i = 0; i < window.localStorage.length; i++) {
      keys.push(window.localStorage.key(i))
    }

    return keys
  },
  setItem(key: string | number, val: unknown): void {
    if (typeof key !== 'string') {
      key = key.toString()
    }

    if (key === undefined || key.trim().length === 0)
      throw new Error('key 参数不能为空或者undefined')

    window.localStorage.setItem(key, JSON.stringify(val))
  },
  getItem<T = unknown>(key: string): T | null {
    const val = window.localStorage.getItem(key)
    return val ? JSON.parse(val) : null
  },
  clearAllKeys(): void {
    window.localStorage.clear()
  },
  removeItem(key: string): void {
    window.localStorage.removeItem(key)
  },
}

export const sessionStorage = {
  getKeys(): (string | null)[] {
    const keys: (string | null)[] = []
    for (let i = 0; i < window.sessionStorage.length; i++) {
      keys.push(window.sessionStorage.key(i))
    }

    return keys
  },
  setItem(key: string | number, val: unknown): void {
    if (typeof key !== 'string') {
      key = key.toString()
    }

    if (key === undefined || key.trim().length === 0)
      throw new Error('key 参数不能为空或者undefined')

    window.sessionStorage.setItem(key, JSON.stringify(val))
  },
  getItem<T = unknown>(key: string): T | null {
    const val = window.sessionStorage.getItem(key)
    return val ? JSON.parse(val) : null
  },
  clearAllKeys(): void {
    window.sessionStorage.clear()
  },
  removeItem(key: string): void {
    window.sessionStorage.removeItem(key)
  },
}
