interface Watermark {
  set: (str: string) => void
  out: () => void
}

const watermark: Watermark = {} as Watermark

const setWatermark = (str: string): string => {
  const id = '1.23452384164.123412415'

  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id)!)
  }

  const can = document.createElement('canvas')
  can.width = 200
  can.height = 120

  const cans = can.getContext('2d')!
  cans.rotate((-15 * Math.PI) / 150)
  cans.font = '18px Vedana'
  cans.fillStyle = 'rgba(200, 200, 200, 0.20)'
  cans.textAlign = 'left'
  cans.textBaseline = 'middle'
  cans.fillText(str, can.width / 8, can.height / 2)

  const div = document.createElement('div')
  div.id = id
  div.style.pointerEvents = 'none'
  div.style.top = '35px'
  div.style.left = '200px'
  div.style.position = 'fixed'
  div.style.zIndex = '100000'
  div.style.width = document.documentElement.clientWidth + 'px'
  div.style.height = document.documentElement.clientHeight + 'px'
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat'
  document.body.appendChild(div)
  return id
}

// 该方法只允许调用一次
watermark.set = (str: string): void => {
  let id = setWatermark(str)
  setInterval(() => {
    if (document.getElementById(id) === null) {
      id = setWatermark(str)
    }
  }, 500)
  window.onresize = () => {
    setWatermark(str)
  }
}

const outWatermark = (id: string): void => {
  if (document.getElementById(id) !== null) {
    const div = document.getElementById(id)!
    div.style.display = 'none'
  }
}

watermark.out = (): void => {
  const str = '1.23452384164.123412415'
  outWatermark(str)
}

export default watermark
