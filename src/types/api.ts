// API response types
export interface ApiResponse<T = any> {
  code: number
  msg: string
  data: T
  success?: boolean
}

// Common types
export interface PaginationParams {
  pageIndex: number
  pageSize: number
  count?: number
}

// User types
export interface UserData {
  userId?: string | number
  username?: string
  password?: string
  nickName?: string
  phone?: string
  email?: string
  sex?: string
  status?: string | number
  deptId?: string | number
  postId?: string | number[]
  roleId?: string | number[]
}

// Dict types
export interface DictData {
  dictId?: string | number
  dictCode?: string
  dictType?: string
  dictLabel?: string
  dictValue?: string
  dictSort?: number
  status?: number
  remark?: string
}

export interface DictTypeData {
  id?: string | number
  dictName?: string
  dictType?: string
  status?: number
  remark?: string
}

// Menu types
export interface MenuData {
  menuId?: string | number
  title?: string
  menuName?: string
  path?: string
  component?: string
  permission?: string
  parentId?: string | number
  menuType?: string
  sort?: number
  isFrame?: number
  visible?: string | number
  icon?: string
  apis?: any[]
}

// Api types
export interface ApiData {
  id?: string | number
  title?: string
  method?: string
  path?: string
}

// Generic form types
export interface FormInstance {
  validate: (callback: (valid: boolean) => void) => void
  resetFields: () => void
}
