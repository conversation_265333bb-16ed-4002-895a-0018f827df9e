<template>
  <a-dropdown placement="bottomLeft" :overlay-style="{ top: '52px' }">
    <a-space class="avatar">
      <a-avatar :size="32" :style="{ backgroundColor: '#1890ff' }">
        <img alt="avatar" :src="userInfo?.avatar" />
      </a-avatar>
      <a-typography-text strong>{{ userInfo?.name }}</a-typography-text>
    </a-space>
    <template #overlay>
      <a-menu>
        <a-menu-item @click="$router.push('/profile')">
          <template #icon>
            <SettingOutlined />
          </template>
          个人设置
        </a-menu-item>
        <a-menu-item @click="handleLogout">
          <template #icon>
            <LogoutOutlined />
          </template>
          退出登录
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { SettingOutlined, LogoutOutlined } from '@ant-design/icons-vue'
import { useUserStore } from '@/store/userInfo'
import { usePermissionStore } from '@/store/permission'

const store = useUserStore()
const permissionStore = usePermissionStore()
const router = useRouter()

const { userInfo } = storeToRefs(store)

const handleLogout = (): void => {
  Modal.warning({
    title: '提示',
    content: '确定注销并退出登录系统吗？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      window.sessionStorage.clear()
      store.userLogout()
      permissionStore.ClearMenuList()
      router.push('/login')
    },
  })
}
</script>

<style lang="scss" scoped>
.avatar {
  // padding: 0px 5px;
  // line-height: 50px;
  // text-align: center;
  // transition: all 0.3s ease-in-out;
  // cursor: pointer;
  // &:hover {
  //   background-color: #e5e5e5;
  // }
}
</style>
