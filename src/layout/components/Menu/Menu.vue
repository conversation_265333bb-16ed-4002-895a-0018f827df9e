<template>
  <a-menu
    class="menu"
    @click="handleMenuClick"
    :default-open-keys="['/admin']"
    :selected-keys="defaultSelectKeys"
    mode="inline"
    theme="light"
  >
    <sub-menu :menu-list="permissionStore.menuList" />
  </a-menu>
</template>

<script setup lang="ts">
import { usePermissionStore } from '@/store/permission'
import SubMenu from './SubMenu.vue'

const permissionStore = usePermissionStore()
const route = useRoute()
const router = useRouter()

// 默认菜单选中
const defaultSelectKeys = ref<string[]>([])

// 刷新保持菜单选中
const keepDefaultSelect = (): void => {
  defaultSelectKeys.value = []
  defaultSelectKeys.value.push(route.fullPath)
}

// 修复菜单点击事件处理
const handleMenuClick = (menuInfo: { key: string }): void => {
  console.log('Menu clicked:', menuInfo.key)
  router.push(menuInfo.key)
}

// 监听路由变化，更新选中菜单
watch(
  () => route.fullPath,
  (newPath: string) => {
    defaultSelectKeys.value = [newPath]
  },
)

onBeforeMount(() => {
  keepDefaultSelect()
})
</script>

<style lang="scss">
/* 移除 Arco Design 特有的样式 */
.arco-menu-indent {
  width: 30px;
}
</style>
