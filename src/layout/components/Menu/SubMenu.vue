<template>
  <template v-for="menu in props.menuList" :key="menu.menuId">
    <a-sub-menu
      v-if="menu.children && menu.menuType === 'M' && menu.visible === '0'"
      :key="menu.path"
    >
      <template #icon>
        <component :is="menu.icon" />
      </template>
      <template #title>{{ menu.title }}</template>
      <sub-menu :menu-list="menu.children" />
    </a-sub-menu>
    <a-menu-item
      v-if="menu.menuType === 'C' && menu.visible && menu.visible === '0'"
      :key="menu.path"
    >
      <template #icon v-if="menu.icon">
        <component :is="menu.icon" />
      </template>
      {{ menu.title }}
    </a-menu-item>
  </template>
</template>

<script setup lang="ts" name="SubMenuComponent">
// import { computed } from 'vue'

interface MenuItem {
  menuId: string | number
  path: string
  title: string
  menuType: 'M' | 'C'
  visible: string
  icon?: string
  children?: MenuItem[]
  [key: string]: unknown
}

interface Props {
  menuList: MenuItem[]
}

const props = withDefaults(defineProps<Props>(), {
  menuList: () => [],
})
</script>
