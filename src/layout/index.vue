<template>
  <a-layout class="min-h-screen layout-container">
    <!-- Header -->
    <a-layout-header class="header-container">
      <div class="header-content">
        <div class="header-left">
          <div class="logo-section">
            <!-- <img :src="store.sysConfig?.sys_app_logo" class="logo-img" /> -->
            <span class="logo-title">迅雷基础服务后台</span>
          </div>
        </div>
        <div class="header-right">
          <a-button shape="circle" @click="handleDarkTheme" class="theme-btn">
            <template #icon>
              <component :is="!darkTheme ? BulbOutlined : BulbFilled"></component>
            </template>
          </a-button>
          <Avatar />
        </div>
      </div>
    </a-layout-header>

    <!-- Body Layout -->
    <a-layout>
      <!-- Sider -->
      <a-layout-sider
        :trigger="null"
        :width="220"
        :collapsed="collapsed"
        collapsible
        class="sider-container"
      >
        <div class="sider-content">
          <div class="collapse-btn" @click="onCollapse">
            <MenuFoldOutlined v-if="!collapsed" />
            <MenuUnfoldOutlined v-else />
          </div>
          <Menu :collapsed="collapsed" />
        </div>
      </a-layout-sider>

      <!-- Content -->
      <a-layout-content class="content-container">
        <tag-view />
        <div class="main-content">
          <AppMain />
        </div>
      </a-layout-content>
    </a-layout>

    <!-- Footer -->
    <a-layout-footer class="footer-container">
      <div class="footer-content">© 2024 迅雷客服系统. All rights reserved.</div>
    </a-layout-footer>
  </a-layout>
</template>

<script setup lang="ts">
import { AppMain } from './components'
import Menu from './components/Menu/Menu.vue'
import TagView from './components/TagView/TagView.vue'
import Avatar from './components/Avatar/index.vue'
import { useUserStore } from '@/store/userInfo'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BulbOutlined,
  BulbFilled,
} from '@ant-design/icons-vue'

const store = useUserStore()
const collapsed = ref<boolean>(false)
const darkTheme = ref<boolean>(false)

const onCollapse = (): void => {
  collapsed.value = !collapsed.value
}

// 切换亮色和暗色
const handleDarkTheme = (): void => {
  const theme = document.body.getAttribute('arco-theme')
  if (!theme) {
    document.body.setAttribute('arco-theme', 'dark')
    darkTheme.value = true
  } else {
    document.body.removeAttribute('arco-theme')
    darkTheme.value = false
  }
}
</script>

<style lang="scss">
@import '../style/index.scss';
@import '../style/transition.scss';
@import '../style/dark-theme.scss';

.layout-container {
  .ant-layout-header {
    background: linear-gradient(90deg, #2d55a0, #3b6ab8);
    padding: 0;
    height: auto;
    line-height: normal;
  }
  .sider-container {
    color: #808080;
    background-color: #fff;
  }
}
/* Header Styles */
.header-container {
  background: linear-gradient(135deg, #2d5aa0 0%, #1c3f7a 100%);
  padding: 0;
  height: auto;
  border-bottom: 1px solid #1c3f7a;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 64px;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;

  .logo-img {
    height: 32px;
    width: auto;
  }

  .logo-title {
    margin-left: 12px;
    color: white;
    font-size: 18px;
    font-weight: bold;
  }
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.theme-btn {
  background-color: transparent;
  border-color: rgba(255, 255, 255, 0.3);
  color: white;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
  }
}

.sider-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.collapse-btn {
  height: 48px;
  width: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Content Styles */
.content-container {
  background-color: #f0f2f5;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 64px - 48px); /* 减去 header 和 footer 高度 */
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #f0f2f5;
}

/* Footer Styles */
.footer-container {
  background-color: #2d5aa0;
  border-top: 1px solid #1c3f7a;
  padding: 12px 0;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-content {
  text-align: center;
  color: white;
  font-size: 12px;
}

/* Ant Design Overrides */
.ant-layout {
  background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .logo-title {
    display: none;
  }

  .main-content {
    padding: 12px;
  }
}

@media (max-width: 576px) {
  .header-content {
    padding: 0 12px;
  }

  .logo-img {
    height: 28px;
  }

  .theme-btn {
    padding: 4px 8px;
    font-size: 12px;
  }
}
</style>
