import { useUserStore } from '@/store/userInfo'

interface PermissionBinding {
  value: string
}

export default {
  checkPermission(el: HTMLElement, binding: PermissionBinding): void {
    const store = useUserStore()
    const { value } = binding
    const all_permission = '*:*:*'
    const permissions = (store.userInfo?.permissions as string[]) || []

    if (typeof value === 'string') {
      const hasPermission = permissions.some((permission: string) => {
        return all_permission === permission || value === permission
      })

      if (!hasPermission) {
        if (el.parentNode) {
          el.parentNode.removeChild(el)
        }
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  },
}
