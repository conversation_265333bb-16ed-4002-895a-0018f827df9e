@tailwind base;
@tailwind components;
@tailwind utilities;

a,
input,
*::before {
  margin: 0;
  padding: 0;
}
*,
*::before,
*::after {
  box-sizing: border-box;
}
img {
  vertical-align: middle;
}
ul {
  padding: 0px 0px 0px 16px;
}
// li {
//   list-style: none;
// }

// main-container 全局样式
.app-container {
  flex: 1;
  padding: 20px 20px 12px 20px;
  background-color: #fff;
  border-radius: 5px;
}

// 表格操作栏样式
.arco-table-th:last-child .arco-table-th-item-title {
  margin-left: 16px;
}

// 数字输入框样式
.arco-input-number .arco-input {
  text-align: center;
}

// Table 操作栏样式覆盖
// .arco-table-th:last-child .arco-table-cell {
//   margin-left: 15px;
// }

// 滚动条整体部分
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: transparent;
}

// 滚动条外层轨道
::-webkit-scrollbar-track {
  border-radius: 10px;
}

// 滚动条滑块
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: rgba(157, 165, 183, 0.4);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(157, 165, 183, 0.7);
}

// 链接标签样式
a {
  text-decoration: none;
  color: #165dff;
}

a:hover {
  color: #4080ff;
}

// 表格操作栏样式
.action {
  margin-bottom: 16px;
}

// 卡片样式
.cardStyle {
  transition: all 0.3s ease;
  //box-shadow: 0 4px 4px rgba(46, 35, 94, 0.07);
  box-shadow:
    #919eab4d 0 0 2px,
    #919eab1f 0 12px 24px -4px;
  &:hover {
    transition: all 0.3s ease;
    transform: translateY(-2px);
    box-shadow:
      #919eab4d 0 0 2px,
      #919eab1f 0 12px 24px -4px;
  }
}

// #########################################
// 定义 主页面顶部列表标题与内容样式
// #########################################
// 顶部信息页面标题
.akaInfoTitle {
  font-size: 30px;
}
// 顶部信息页面描述
.akaInfoDesc {
  color: #79879c !important;
  font-size: 12px;
  margin-top: 6px;
}
