import { defineStore } from 'pinia'
import { ref } from 'vue'
import { sessionStorage } from '@/utils/storage'
import type { RouteLocationNormalized } from 'vue-router'

interface TagView extends Partial<RouteLocationNormalized> {
  title?: string
  meta?: {
    title?: string
    noCache?: boolean
    [key: string]: unknown
  }
}

export const useTagViewStore = defineStore('tagView', () => {
  const visitedViews = ref<TagView[]>(sessionStorage.getItem('tagList') || [])
  const cachedViews = ref<string[]>([])

  function addVisitedViews(view: RouteLocationNormalized): void {
    console.log(view.meta?.title)

    // Akiraka 20240228 限制标签数量
    if (visitedViews.value.length >= 20) {
      visitedViews.value.splice(0, 1)
    }

    if (visitedViews.value.some((v) => v.path === view.path || v.meta?.title === view.meta?.title))
      return

    visitedViews.value.push(
      Object.assign({}, view, {
        title: view.meta?.title || 'no-name',
      }),
    )
    sessionStorage.setItem('tagList', visitedViews.value)
  }

  function delVisitedViews(view: RouteLocationNormalized): void {
    for (const [i, v] of visitedViews.value.entries()) {
      if (v.path === view.path) {
        visitedViews.value.splice(i, 1)
        break
      }
    }
    sessionStorage.setItem('tagList', visitedViews.value)
  }

  function delAllVisitedViews(view: RouteLocationNormalized): void {
    visitedViews.value = visitedViews.value.filter((v) => v.path === view.path)
    sessionStorage.setItem('tagList', visitedViews.value)
  }

  function delLeftVisitedViews(view: RouteLocationNormalized): void {
    for (const [i, v] of visitedViews.value.entries()) {
      if (v.path === view.path) {
        visitedViews.value = visitedViews.value.slice(i)
        break
      }
    }
    sessionStorage.setItem('tagList', visitedViews.value)
  }

  function delRightVisitedViews(view: RouteLocationNormalized): void {
    for (const [i, v] of visitedViews.value.entries()) {
      if (v.path === view.path) {
        visitedViews.value = visitedViews.value.slice(0, i + 1)
        break
      }
    }
    sessionStorage.setItem('tagList', visitedViews.value)
  }

  function addCachedViews(view: RouteLocationNormalized): void {
    if (cachedViews.value.includes(view.name as string)) return
    if (!view.meta?.noCache) cachedViews.value.push(view.name as string)
  }

  function delCachedViews(view: RouteLocationNormalized): void {
    visitedViews.value = visitedViews.value.filter((v) => v !== view.name)
  }

  function delCachedVisitedViews(): void {
    cachedViews.value = []
  }

  return {
    visitedViews,
    cachedViews,
    addVisitedViews,
    delVisitedViews,
    delAllVisitedViews,
    delLeftVisitedViews,
    delRightVisitedViews,
  }
})
