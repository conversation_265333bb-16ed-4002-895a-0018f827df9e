import { defineStore } from 'pinia'
import { getUserMenuRole } from '@/api/admin/login'
import type { RouteRecordRaw } from 'vue-router'

interface MenuItem {
  menuType: 'M' | 'C'
  path: string
  menuName: string
  component?: string
  title: string
  permission: string
  children?: MenuItem[]
  visible?: number
}

interface PermissionState {
  addRouters: RouteRecordRaw[]
  menuList: MenuItem[]
}

const modules = import.meta.glob('../views/**/*.vue')
console.log(modules, 'modules')

export const usePermissionStore = defineStore('permisson', {
  state: (): PermissionState => {
    return {
      addRouters: [],
      menuList: [],
    }
  },
  getters: {
    getRoutes: (state): RouteRecordRaw[] => state.addRouters,
  },
  actions: {
    setMenuList(menus: MenuItem[]): void {
      this.menuList = menus
    },
    GenerateRoutes(routeList: MenuItem[]): RouteRecordRaw[] {
      const routes: RouteRecordRaw[] = []

      routeList.forEach((item) => {
        const route: RouteRecordRaw = {}
        // if (item.visible == 0) {
        if (item.menuType === 'M' || item.menuType === 'C') {
          route.path = item.path
          route.name = item.menuName
          if (item.menuType === 'M') {
            console.log(modules[`../views/index.vue`], 'modules')
            route.component = modules[`../views/index.vue`]
          } else if (item.menuType === 'C') {
            route.component =
              modules[`../views${item.component}.vue`] || modules['../views/error-page/888.vue']
          }
          route.meta = {
            title: item.title,
            permission: item.permission,
          }
        }

        if (item.children) {
          route.children = this.GenerateRoutes(item.children)
        }
        routes.push(route)
        // }
      })

      return routes
    },
    async getMenuRole(): Promise<void> {
      try {
        const res = await getUserMenuRole()
        this.setMenuList(res.data)
        this.addRouters = await this.GenerateRoutes(res.data)
      } catch (error) {
        // 如果 API 失败，使用临时菜单数据进行测试
        console.warn('Failed to fetch menu from API, using mock data:', error)
        const mockMenuData: MenuItem[] = [
          {
            menuType: 'C',
            path: '/dashboard',
            menuName: 'dashboard',
            component: '/dashboard',
            title: '仪表板',
            permission: 'dashboard:view',
            visible: 0,
          },
          {
            menuType: 'M',
            path: '/admin',
            menuName: 'admin',
            title: '系统管理',
            permission: 'admin:view',
            visible: 0,
            children: [
              {
                menuType: 'C',
                path: '/admin/user',
                menuName: 'user',
                component: '/admin/user',
                title: '用户管理',
                permission: 'admin:user:view',
                visible: 0,
              },
              {
                menuType: 'C',
                path: '/admin/menu',
                menuName: 'menu',
                component: '/admin/menu',
                title: '菜单管理',
                permission: 'admin:menu:view',
                visible: 0,
              },
            ],
          },
        ]
        this.setMenuList(mockMenuData)
        this.addRouters = await this.GenerateRoutes(mockMenuData)
      }
    },
    ClearMenuList(): void {
      this.menuList = []
    },
  },
})
