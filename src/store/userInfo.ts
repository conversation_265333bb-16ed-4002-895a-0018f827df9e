import { defineStore } from 'pinia'
import { storage } from '@/utils/storage'
import { getInfo } from '@/api/admin/sys-user'
import { getAppConfig } from '@/api/admin/login'

interface UserInfo {
  userId: string
  roles: string[]
  [key: string]: unknown
}

interface SysConfig {
  sys_app_name: string
  [key: string]: unknown
}

interface UserState {
  token: string | null
  uid: string | null
  sysConfig: SysConfig | null
  userInfo: UserInfo | null
}

export const useUserStore = defineStore('user', {
  state: (): UserState => {
    return {
      token: storage.getItem('token') || null,
      uid: storage.getItem('uid') || null,
      sysConfig: null,
      userInfo: null,
    }
  },
  getters: {
    roles: (state): string[] | undefined => state?.userInfo?.roles,
  },
  actions: {
    setToken(token: string): void {
      this.token = token
      // Akiraka 20230504 设置缓存 token
      storage.setItem('token', token)
    },
    async getUserInfo(): Promise<void> {
      try {
        const { data } = await getInfo()
        // storage.setItem('userInfo', res.data);
        storage.setItem('uid', data.userId)
        this.userInfo = data
      } catch (err) {
        console.error(err)
      }
    },
    async getSysConfig(): Promise<void> {
      const sysConfig = storage.getItem<SysConfig>('sysConfig')
      if (sysConfig) {
        this.sysConfig = sysConfig
      } else {
        try {
          const { data, code } = await getAppConfig()
          if (code === 200) {
            storage.setItem('sysConfig', data)
            this.sysConfig = data
          }
        } catch (err) {
          console.error(err)
        }
      }
    },
    userLogout(): void {
      this.token = null
      this.userInfo = null
      // Akiraka 20230504 清除缓存 token
      storage.removeItem('token')
    },
  },
})
